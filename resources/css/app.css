@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* RTL-specific spacing adjustments */
[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* RTL margin adjustments */
[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .ml-10 {
  margin-left: 0;
  margin-right: 2.5rem;
}

[dir="rtl"] .ml-64 {
  margin-left: 0;
  margin-right: 16rem;
}

/* RTL text alignment */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL flex adjustments */
[dir="rtl"] .justify-between {
  flex-direction: row-reverse;
}

/* Arabic font support */
[dir="rtl"] {
  font-family: 'Segoe UI', Tahoma, Arial, Helvetica, sans-serif;
}

/* RTL form adjustments */
[dir="rtl"] .relative .absolute.inset-y-0.left-0 {
  left: auto;
  right: 0;
}

[dir="rtl"] .pl-8 {
  padding-left: 0.75rem;
  padding-right: 2rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

/* RTL table adjustments */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL button spacing */
[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

/* RTL sidebar adjustments */
[dir="rtl"] .w-64 {
  left: auto;
}

[dir="rtl"] .ml-64 {
  margin-left: 0;
  margin-right: 16rem;
}

/* RTL navigation adjustments */
[dir="rtl"] .justify-between {
  flex-direction: row;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  /* Ensure text doesn't get too small on mobile */
  .text-xs {
    font-size: 0.75rem;
  }

  /* Better touch targets for mobile */
  button, .btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve form inputs on mobile */
  input, select, textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile cards */
  .mobile-card {
    padding: 1rem;
    margin-bottom: 0.5rem;
  }

  /* Ensure modals don't overflow on small screens */
  .modal-content {
    max-height: 90vh;
    overflow-y: auto;
  }
}

/* Improve table responsiveness */
@media (max-width: 1024px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
