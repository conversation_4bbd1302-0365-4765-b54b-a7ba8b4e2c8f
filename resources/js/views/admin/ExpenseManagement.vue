<template>
  <dashboard-layout :title="$t('expense_management')">
    <template #sidebar>
      <div v-if="userLoaded">
        <router-link
          to="/admin/expenses"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('expenses') }}
        </router-link>
        <router-link
          to="/admin/incomes"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('incomes') }}
        </router-link>
        <router-link
          to="/admin/users"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('users') }}
        </router-link>
        <router-link
          v-if="isSuperAdmin"
          to="/admin/buildings"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('buildings') }}
        </router-link>
        <router-link
          v-if="!isSuperAdmin"
          to="/admin/my-building"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('my_building') }}
        </router-link>
      </div>
      <div v-else class="text-gray-500 text-sm">
        {{ $t('loading_menu') }}
      </div>
    </template>

    <!-- Filters -->
    <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('expense_type') }}</label>
          <select v-model="filters.type" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option value="">{{ $t('all_types') }}</option>
            <option value="building_services">{{ $t('building_services') }}</option>
            <option value="building_electricity">{{ $t('building_electricity') }}</option>
            <option value="personal_electricity">{{ $t('personal_electricity') }}</option>
            <option value="water">{{ $t('water') }}</option>
            <option value="other">{{ $t('other') }}</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('month') }}</label>
          <select v-model="filters.month" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            <option value="">{{ $t('all_months') }}</option>
            <option value="01">{{ $t('january') }}</option>
            <option value="02">{{ $t('february') }}</option>
            <option value="03">{{ $t('march') }}</option>
            <option value="04">{{ $t('april') }}</option>
            <option value="05">{{ $t('may') }}</option>
            <option value="06">{{ $t('june') }}</option>
            <option value="07">{{ $t('july') }}</option>
            <option value="08">{{ $t('august') }}</option>
            <option value="09">{{ $t('september') }}</option>
            <option value="10">{{ $t('october') }}</option>
            <option value="11">{{ $t('november') }}</option>
            <option value="12">{{ $t('december') }}</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">{{ $t('year') }}</label>
          <input
            type="number"
            v-model="filters.year"
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            min="2023"
            max="2025"
          />
        </div>



        <div class="flex items-end">
          <button
            @click="applyFilters"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            {{ $t('apply_filters') }}
          </button>
        </div>
      </div>

      <div class="mt-4 flex justify-between items-center">
        <router-link
          to="/admin/expenses/create"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          {{ $t('create_expense') }}
        </router-link>

        <button
          @click="generateMonthlyExpenses"
          :disabled="generatingExpenses"
          class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400"
        >
          {{ generatingExpenses ? $t('generating') : $t('generate_monthly_expenses') }}
        </button>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ $t('total_expenses') }}</h3>
        <p class="text-3xl font-bold text-red-600">₪{{ totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ $t('this_month') }}</h3>
        <p class="text-3xl font-bold text-blue-600">₪{{ thisMonthExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ $t('automatic') }}</h3>
        <p class="text-3xl font-bold text-purple-600">₪{{ automaticExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ $t('manual') }}</h3>
        <p class="text-3xl font-bold text-orange-600">₪{{ manualExpenses.toFixed(2) }}</p>
      </div>
    </div>

    <!-- Expenses Table -->
    <data-table
      :title="$t('expense_records')"
      :columns="columns"
      :items="expenses"
      :loading="loading"
    >
      <template #actions="{ item }">
        <div class="flex space-x-2">
          <button
            @click="editExpense(item)"
            class="text-indigo-600 hover:text-indigo-900"
          >
            {{ $t('edit') }}
          </button>
          <button
            @click="deleteExpense(item)"
            class="text-red-600 hover:text-red-900"
          >
            {{ $t('delete') }}
          </button>
        </div>
      </template>
    </data-table>

    <!-- Pagination -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-700">
        {{ $t('showing_results') }} {{ pagination.from }} {{ $t('to') }} {{ pagination.to }} {{ $t('of') }} {{ pagination.total }} {{ $t('results') }}
      </div>
      <div class="flex space-x-2">
        <button
          @click="previousPage"
          :disabled="pagination.currentPage === 1"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          {{ $t('previous') }}
        </button>
        <button
          @click="nextPage"
          :disabled="pagination.currentPage === pagination.lastPage"
          class="px-3 py-1 border rounded disabled:opacity-50"
        >
          {{ $t('next') }}
        </button>
      </div>
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 z-50 overflow-y-auto">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-black bg-opacity-50" @click="closeEditModal"></div>

      <!-- Modal content -->
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
          <!-- Close button -->
          <button
            @click="closeEditModal"
            class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>

          <!-- Modal header -->
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
              {{ $t('edit_expense') }}
            </h3>
          </div>

          <!-- Modal body -->
          <div class="p-6">
            <expense-form
              :expense="selectedExpense"
              :is-edit="true"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="closeEditModal"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import DataTable from '../../components/DataTable.vue';
import ExpenseForm from '../../components/ExpenseForm.vue';
import Notification from '../../components/Notification.vue';
import i18nMixin from '../../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DashboardLayout,
    DataTable,
    ExpenseForm,
    Notification
  },
  data() {
    return {
      loading: false,
      generatingExpenses: false,
      expenses: [],
      selectedExpense: null,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      user: null,
      isSuperAdmin: false,
      userLoaded: false,
      filters: {
        type: '',
        month: '',
        year: new Date().getFullYear()
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: []
    };
  },
  computed: {
    totalExpenses() {
      return this.expenses.reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    thisMonthExpenses() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      return this.expenses
        .filter(expense => {
          return parseInt(expense.month) === currentMonth &&
                 parseInt(expense.year) === currentYear;
        })
        .reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    automaticExpenses() {
      return this.expenses
        .filter(expense => expense.is_automatic === true || expense.is_automatic === 1)
        .reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    manualExpenses() {
      return this.expenses
        .filter(expense => expense.is_automatic === false || expense.is_automatic === 0)
        .reduce((total, expense) => total + parseFloat(expense.amount), 0);
    }
  },
  async created() {
    this.initializeColumns();
    this.initializeUserFromStorage();
    await this.fetchUser();
    this.loadExpenses();
  },
  methods: {
    initializeColumns() {
      this.columns = [
        { key: 'expense_type.name', label: this.$t('type') },
        { key: 'user.name', label: this.$t('neighbor') },
        { key: 'user.apartment_number', label: this.$t('apartment') },
        { key: 'month', label: this.$t('month') },
        { key: 'year', label: this.$t('year') },
        { key: 'amount', label: this.$t('amount') },
        { key: 'is_automatic', label: this.$t('auto') },
        { key: 'notes', label: this.$t('notes') }
      ];
    },
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async fetchUser() {
      try {
        const response = await this.$axios.get('/user');
        this.user = response.data;
        this.isSuperAdmin = this.user.role === 'super_admin';
        this.userLoaded = true;
        localStorage.setItem('user', JSON.stringify(this.user));
      } catch (error) {
        console.error('Error fetching user:', error);
      }
    },
    async loadExpenses() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/expenses', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });

        this.expenses = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError('Error loading expenses');
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadExpenses();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadExpenses();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadExpenses();
      }
    },
    editExpense(expense) {
      this.selectedExpense = expense;
      this.showEditModal = true;
    },
    async deleteExpense(expense) {
      if (confirm('Are you sure you want to delete this expense?')) {
        try {
          await this.$axios.delete(`/expenses/${expense.id}`);
          this.showSuccess('Deleted', 'Expense deleted successfully');
          this.loadExpenses();
        } catch (error) {
          this.showError('Delete Failed', 'Failed to delete expense');
        }
      }
    },
    handleEditSuccess() {
      this.showSuccess('Updated', 'Expense updated successfully');
      this.closeEditModal();
      this.loadExpenses();
    },
    handleEditError(message) {
      this.showError('Update Failed', message);
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedExpense = null;
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    },
    async generateMonthlyExpenses() {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const formattedMonth = currentMonth.toString().padStart(2, '0');

      // Get building info to show the correct monthly fee
      let monthlyFee = 'dynamic amount';
      try {
        if (!this.isSuperAdmin) {
          const buildingResponse = await this.$axios.get('/my-building');
          monthlyFee = `₪${parseFloat(buildingResponse.data.monthly_fee).toFixed(2)}`;
        }
      } catch (error) {
        console.warn('Could not fetch building info for monthly fee display');
      }

      const confirmMessage = this.isSuperAdmin
        ? `Generate monthly expenses for all neighbors for ${formattedMonth}/${currentYear}?`
        : `Generate monthly expenses (${monthlyFee}) for all neighbors in your building for ${formattedMonth}/${currentYear}?`;

      if (confirm(confirmMessage)) {
        this.generatingExpenses = true;
        try {
          await this.$axios.post('/expenses/generate-monthly', {
            month: formattedMonth,
            year: currentYear.toString()
          });

          this.showSuccess('Generated', 'Monthly expenses generated successfully');
          this.loadExpenses();
        } catch (error) {
          this.showError('Generation Failed', error.response?.data?.message || 'Failed to generate monthly expenses');
        } finally {
          this.generatingExpenses = false;
        }
      }
    }
  }
};
</script> 