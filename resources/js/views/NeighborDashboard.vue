<template>
  <dashboard-layout :title="$t('neighbor_dashboard')">
    <template #sidebar>
      <div class="block px-4 py-2 text-gray-600 bg-indigo-50 text-indigo-600 rounded">
        {{ $t('my_financial_summary') }}
      </div>
    </template>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ $t('total_expenses') }}</h3>
        <p class="text-3xl font-bold text-red-600">₪{{ totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ $t('total_payments') }}</h3>
        <p class="text-3xl font-bold text-green-600">₪{{ totalPayments.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-700">{{ $t('outstanding_balance') }}</h3>
        <p class="text-3xl font-bold"
           :class="outstandingBalance > 0 ? 'text-red-600' : outstandingBalance < 0 ? 'text-green-600' : 'text-gray-600'">
          ₪{{ outstandingBalance.toFixed(2) }}
        </p>
      </div>
    </div>

    <!-- My Expenses -->
    <data-table
      :title="$t('my_expenses')"
      :columns="expenseColumns"
      :items="myExpenses"
      :loading="loading"
    />

    <!-- My Income Records -->
    <div class="mt-6">
      <data-table
        :title="$t('my_income_records')"
        :columns="incomeColumns"
        :items="myIncomes"
        :loading="loading"
      />
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';
import Notification from '../components/Notification.vue';
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DashboardLayout,
    DataTable,
    Notification
  },
  data() {
    return {
      loading: false,
      myExpenses: [],
      myIncomes: [],
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      expenseColumns: [],
      incomeColumns: []
    };
  },
  computed: {
    totalExpenses() {
      return this.myExpenses.reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    totalPayments() {
      return this.myIncomes.reduce((total, income) => total + parseFloat(income.amount), 0);
    },
    outstandingBalance() {
      return this.totalExpenses - this.totalPayments;
    }
  },
  async created() {
    this.initializeColumns();
    await this.loadDashboardData();
  },
  methods: {
    initializeColumns() {
      this.expenseColumns = [
        { key: 'expense_type.name', label: this.$t('type') },
        { key: 'amount', label: this.$t('amount') },
        { key: 'month', label: this.$t('month') },
        { key: 'year', label: this.$t('year') },
        { key: 'notes', label: this.$t('notes') }
      ];
      this.incomeColumns = [
        { key: 'amount', label: this.$t('amount') },
        { key: 'payment_date', label: this.$t('payment_date') },
        { key: 'payment_method', label: this.$t('method') },
        { key: 'notes', label: this.$t('notes') }
      ];
    },
    async loadDashboardData() {
      this.loading = true;
      try {
        // Get current user from localStorage
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        console.log('Current user:', user);

        if (!user.id) {
          this.showError(this.$t('authentication_error'), this.$t('user_not_found'));
          return;
        }

        // Load user's expenses - filter by current user
        console.log('Loading expenses for user ID:', user.id);
        const expensesResponse = await this.$axios.get('/expenses', {
          params: {
            user_id: user.id
          }
        });
        console.log('Expenses response:', expensesResponse.data);

        if (expensesResponse.data.data) {
          this.myExpenses = expensesResponse.data.data;
        } else if (Array.isArray(expensesResponse.data)) {
          this.myExpenses = expensesResponse.data;
        } else {
          this.myExpenses = [];
        }
        console.log('My expenses:', this.myExpenses);

        // Load user's income records - filter by current user
        console.log('Loading incomes for user ID:', user.id);
        const incomesResponse = await this.$axios.get('/incomes', {
          params: {
            user_id: user.id
          }
        });
        console.log('Incomes response:', incomesResponse.data);

        if (incomesResponse.data.data) {
          this.myIncomes = incomesResponse.data.data;
        } else if (Array.isArray(incomesResponse.data)) {
          this.myIncomes = incomesResponse.data;
        } else {
          this.myIncomes = [];
        }
        console.log('My incomes:', this.myIncomes);

        // Show success message if data loaded
        if (this.myExpenses.length === 0 && this.myIncomes.length === 0) {
          this.showSuccess(this.$t('data_loaded'), this.$t('no_financial_records'));
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error);
        this.showError(this.$t('error_loading_dashboard'), error.response?.data?.message || this.$t('failed_load_financial_data'));
      } finally {
        this.loading = false;
      }
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script> 