<template>
  <dashboard-layout :title="$t('admin_dashboard')">
    <template #sidebar>
      <div v-if="userLoaded">
        <router-link
          to="/admin/expenses"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('expenses') }}
        </router-link>
        <router-link
          to="/admin/incomes"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('incomes') }}
        </router-link>
        <router-link
          to="/admin/users"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('users') }}
        </router-link>
        <router-link
          v-if="isSuperAdmin"
          to="/admin/buildings"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('buildings') }}
        </router-link>
        <router-link
          v-if="!isSuperAdmin"
          to="/admin/my-building"
          class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
          active-class="bg-indigo-50 text-indigo-600"
        >
          {{ $t('my_building') }}
        </router-link>
      </div>
      <div v-else class="text-gray-500 text-sm">
        {{ $t('loading_menu') }}
      </div>
    </template>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6">
      <div class="bg-white p-4 lg:p-6 rounded-lg shadow-sm">
        <h3 class="text-sm lg:text-lg font-semibold text-gray-700">{{ isSuperAdmin ? $t('total_buildings') : $t('total_expenses') }}</h3>
        <p class="text-2xl lg:text-3xl font-bold text-blue-600">{{ isSuperAdmin ? totalBuildings : '₪' + totalExpenses.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-4 lg:p-6 rounded-lg shadow-sm">
        <h3 class="text-sm lg:text-lg font-semibold text-gray-700">{{ isSuperAdmin ? $t('total_admins') : $t('total_income') }}</h3>
        <p class="text-2xl lg:text-3xl font-bold text-green-600">{{ isSuperAdmin ? totalAdmins : '₪' + totalIncome.toFixed(2) }}</p>
      </div>
      <div class="bg-white p-4 lg:p-6 rounded-lg shadow-sm">
        <h3 class="text-sm lg:text-lg font-semibold text-gray-700">{{ isSuperAdmin ? $t('total_neighbors') : $t('total_users') }}</h3>
        <p class="text-2xl lg:text-3xl font-bold text-purple-600">{{ isSuperAdmin ? totalNeighbors : totalUsers }}</p>
      </div>
      <div class="bg-white p-4 lg:p-6 rounded-lg shadow-sm">
        <h3 class="text-sm lg:text-lg font-semibold text-gray-700">{{ isSuperAdmin ? $t('active_buildings') : $t('outstanding_balance') }}</h3>
        <p class="text-2xl lg:text-3xl font-bold text-orange-600">{{ isSuperAdmin ? activeBuildings : '₪' + outstandingBalance.toFixed(2) }}</p>
      </div>
    </div>

    <!-- Super Admin Tables -->
    <div v-if="isSuperAdmin && !loading" class="space-y-6">
      <!-- Buildings Table -->
      <data-table
        :title="$t('buildings')"
        :columns="buildingColumns"
        :items="buildings"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-2" v-if="item && item.id">
            <router-link
              :to="`/admin/buildings/${item.id}/edit`"
              class="text-indigo-600 hover:text-indigo-900 text-sm px-2 py-1 rounded hover:bg-indigo-50"
            >
              {{ $t('edit') }}
            </router-link>
            <button
              @click="deleteBuilding(item)"
              class="text-red-600 hover:text-red-900 text-sm px-2 py-1 rounded hover:bg-red-50"
            >
              {{ $t('delete') }}
            </button>
          </div>
        </template>
        <template #header-actions>
          <router-link
            to="/admin/buildings/create"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
          >
            {{ $t('add_building') }}
          </router-link>
        </template>
      </data-table>

      <!-- Admins Table -->
      <data-table
        :title="$t('admins')"
        :columns="adminColumns"
        :items="admins"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-2" v-if="item && item.id">
            <button
              @click="editAdmin(item)"
              class="text-indigo-600 hover:text-indigo-900 text-sm px-2 py-1 rounded hover:bg-indigo-50"
            >
              {{ $t('edit') }}
            </button>
            <button
              @click="deleteAdmin(item)"
              class="text-red-600 hover:text-red-900 text-sm px-2 py-1 rounded hover:bg-red-50"
            >
              {{ $t('delete') }}
            </button>
          </div>
        </template>
        <template #header-actions>
          <router-link
            to="/admin/users/create"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
          >
            {{ $t('add_admin') }}
          </router-link>
        </template>
      </data-table>
    </div>

    <!-- Regular Admin Table -->
    <div v-else-if="!loading">
      <data-table
        :title="$t('neighbor_financial_summary')"
        :columns="neighborColumns"
        :items="neighborSummary"
        :loading="loading"
      />
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="text-gray-500">{{ $t('loading_dashboard_data') }}</div>
    </div>
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';
import i18nMixin from '../mixins/i18nMixin.js';

export default {
  mixins: [i18nMixin],
  components: {
    DashboardLayout,
    DataTable
  },
  data() {
    return {
      loading: true, // Start with loading true
      totalExpenses: 0,
      totalIncome: 0,
      totalUsers: 0,
      totalBuildings: 0,
      totalAdmins: 0,
      totalNeighbors: 0,
      activeBuildings: 0,
      allExpenses: [],
      allIncomes: [],
      buildings: [],
      admins: [],
      neighborColumns: [],
      buildingColumns: [],
      adminColumns: [],
      isSuperAdmin: false,
      userLoaded: false
    };
  },
  computed: {
    outstandingBalance() {
      return this.totalExpenses - this.totalIncome;
    },
    neighborSummary() {
      // Create a map to group expenses and incomes by user
      const userMap = new Map();

      // Process expenses
      if (Array.isArray(this.allExpenses)) {
        this.allExpenses.forEach(expense => {
          if (expense && expense.user && expense.user.id) {
            const userId = expense.user.id;
            if (!userMap.has(userId)) {
              userMap.set(userId, {
                id: userId,
                name: expense.user.name || 'Unknown',
                apartment_number: expense.user.apartment_number || 'N/A',
                total_expenses: 0,
                total_income: 0
              });
            }
            userMap.get(userId).total_expenses += parseFloat(expense.amount || 0);
          }
        });
      }

      // Process incomes
      if (Array.isArray(this.allIncomes)) {
        this.allIncomes.forEach(income => {
          if (income && income.user && income.user.id) {
            const userId = income.user.id;
            if (!userMap.has(userId)) {
              userMap.set(userId, {
                id: userId,
                name: income.user.name || 'Unknown',
                apartment_number: income.user.apartment_number || 'N/A',
                total_expenses: 0,
                total_income: 0
              });
            }
            userMap.get(userId).total_income += parseFloat(income.amount || 0);
          }
        });
      }

      // Convert to array and calculate outstanding balance
      return Array.from(userMap.values()).map(user => ({
        ...user,
        outstanding_balance: user.total_expenses - user.total_income
      })).sort((a, b) => {
        const aApt = a.apartment_number || '';
        const bApt = b.apartment_number || '';
        return aApt.localeCompare(bApt);
      });
    }
  },
  async created() {
    // First try to get user data from localStorage for immediate role determination
    this.initializeUserFromStorage();
    this.initializeColumns();
    await this.loadDashboardData();
  },
  methods: {
    initializeColumns() {
      this.neighborColumns = [
        { key: 'name', label: this.$t('neighbor') },
        { key: 'apartment_number', label: this.$t('apartment') },
        { key: 'total_expenses', label: this.$t('total_expenses') },
        { key: 'total_income', label: this.$t('total_income') },
        { key: 'outstanding_balance', label: this.$t('outstanding_balance') }
      ];
      this.buildingColumns = [
        { key: 'name', label: this.$t('building_name') },
        { key: 'city', label: this.$t('city') },
        { key: 'monthly_fee', label: this.$t('monthly_fee') },
        { key: 'admin_count', label: this.$t('admins') },
        { key: 'neighbor_count', label: this.$t('neighbors') }
      ];
      this.adminColumns = [
        { key: 'name', label: this.$t('name') },
        { key: 'email', label: this.$t('email') },
        { key: 'building.name', label: this.$t('building') },
        { key: 'created_at', label: this.$t('created') }
      ];
    },
    initializeUserFromStorage() {
      try {
        const userData = JSON.parse(localStorage.getItem('user') || 'null');
        if (userData && userData.role) {
          this.isSuperAdmin = userData.role === 'super_admin';
          this.userLoaded = true;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    },
    async loadDashboardData() {
      this.loading = true;
      try {
        // Load user data to check role (and update localStorage if needed)
        const userResponse = await this.$axios.get('/user');
        if (userResponse.data) {
          this.isSuperAdmin = userResponse.data.role === 'super_admin';
          this.userLoaded = true;
          // Update localStorage with fresh user data
          localStorage.setItem('user', JSON.stringify(userResponse.data));
        }

        if (this.isSuperAdmin) {
          // Load buildings data for super admin
          const buildingsResponse = await this.$axios.get('/buildings');
          if (buildingsResponse.data && Array.isArray(buildingsResponse.data)) {
            this.buildings = buildingsResponse.data.filter(building => building && building.id).map(building => ({
              ...building,
              monthly_fee: `₪${parseFloat(building.monthly_fee || 0).toFixed(2)}`,
              admin_count: 0, // Will be calculated from users
              neighbor_count: 0 // Will be calculated from users
            }));
            this.totalBuildings = this.buildings.length;
            this.activeBuildings = this.buildings.filter(b => b.admin_count > 0).length;
          } else {
            this.buildings = [];
          }

          // Load all users data for super admin
          const usersResponse = await this.$axios.get('/admin/users');
          if (usersResponse.data.data && Array.isArray(usersResponse.data.data)) {
            const allUsers = usersResponse.data.data.filter(user => user && user.id);

            // Filter admins
            this.admins = allUsers.filter(user => user.role === 'admin');
            this.totalAdmins = this.admins.length;

            // Count neighbors
            this.totalNeighbors = allUsers.filter(user => user.role === 'neighbor').length;

            // Calculate building user counts
            this.buildings.forEach(building => {
              building.admin_count = allUsers.filter(user =>
                user.building_id === building.id && user.role === 'admin'
              ).length;
              building.neighbor_count = allUsers.filter(user =>
                user.building_id === building.id && user.role === 'neighbor'
              ).length;
            });

            // Update active buildings count
            this.activeBuildings = this.buildings.filter(b => b.admin_count > 0).length;
          }
        } else {
          // Load regular admin data
          // Load all expenses data
          const expensesResponse = await this.$axios.get('/expenses');
          if (expensesResponse.data.data && Array.isArray(expensesResponse.data.data)) {
            this.allExpenses = expensesResponse.data.data.filter(expense => expense && expense.id);
            this.totalExpenses = this.allExpenses.reduce((total, expense) =>
              total + parseFloat(expense.amount || 0), 0);
          } else {
            this.allExpenses = [];
          }

          // Load all incomes data
          const incomesResponse = await this.$axios.get('/incomes');
          if (incomesResponse.data.data && Array.isArray(incomesResponse.data.data)) {
            this.allIncomes = incomesResponse.data.data.filter(income => income && income.id);
            this.totalIncome = this.allIncomes.reduce((total, income) =>
              total + parseFloat(income.amount || 0), 0);
          } else {
            this.allIncomes = [];
          }

          // Load users data
          const usersResponse = await this.$axios.get('/admin/users');
          if (usersResponse.data.data) {
            this.totalUsers = usersResponse.data.data.length;
          }
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        this.loading = false;
      }
    },
    async deleteBuilding(building) {
      if (confirm(this.$t('confirm_delete_building', { name: building.name }))) {
        try {
          await this.$axios.delete(`/buildings/${building.id}`);
          this.buildings = this.buildings.filter(b => b.id !== building.id);
          this.totalBuildings = this.buildings.length;
        } catch (error) {
          console.error('Error deleting building:', error);
          alert(this.$t('failed_delete_building'));
        }
      }
    },
    editAdmin(admin) {
      // Navigate to user edit page or open modal
      this.$router.push(`/admin/users/${admin.id}/edit`);
    },
    async deleteAdmin(admin) {
      if (confirm(this.$t('confirm_delete_admin', { name: admin.name }))) {
        try {
          await this.$axios.delete(`/admin/users/${admin.id}`);
          this.admins = this.admins.filter(a => a.id !== admin.id);
          this.totalAdmins = this.admins.length;
        } catch (error) {
          console.error('Error deleting admin:', error);
          alert(this.$t('failed_delete_admin'));
        }
      }
    }
  }
};
</script>
