<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Income;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class IncomeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = Income::with(['user']);

        // Filter by user's building (unless super admin)
        if ($user->role !== 'super_admin') {
            $query->where('building_id', $user->building_id);
        }

        // Apply filters
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }



        if ($request->filled('date_from')) {
            $query->where('payment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('payment_date', '<=', $request->date_to);
        }

        // Order by latest
        $query->latest();

        // Paginate results
        $incomes = $query->paginate(50);

        return response()->json($incomes);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'notes' => 'nullable|string',
        ]);

        // Auto-set building_id from logged-in user
        $validated['building_id'] = $user->building_id;

        // Validate that the selected user belongs to the same building (unless super admin)
        if ($user->role !== 'super_admin') {
            $selectedUser = \App\Models\User::find($validated['user_id']);
            if (!$selectedUser || $selectedUser->building_id !== $user->building_id) {
                return response()->json(['message' => 'You can only create income records for users in your building.'], 403);
            }
        }

        // Set default status
        $validated['status'] = 'received';

        $income = Income::create($validated);
        $income->load(['user']);

        return response()->json($income, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Income $income): JsonResponse
    {
        $income->load(['user']);
        return response()->json($income);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Income $income): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'notes' => 'nullable|string',

        ]);

        // Keep the original building_id (don't allow changing it)
        $income->update($validated);
        $income->load(['user']);

        return response()->json($income);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Income $income): JsonResponse
    {
        $income->delete();
        return response()->json(null, 204);
    }

    public function getSummary(): JsonResponse
    {
        $summary = Income::selectRaw('
            DATE_FORMAT(payment_date, "%Y-%m") as period,
            SUM(amount) as total_amount,
            COUNT(*) as total_incomes,
            SUM(CASE WHEN status = "received" THEN 1 ELSE 0 END) as received_incomes
        ')
        ->groupBy('period')
        ->orderBy('period', 'desc')
        ->get();

        return response()->json($summary);
    }
}
